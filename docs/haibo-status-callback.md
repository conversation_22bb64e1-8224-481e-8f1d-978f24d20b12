# 海博配送状态回调功能

## 功能概述

本功能实现了配送商系统向海博系统的配送状态回调，满足以下三种场景的回调需求：

1. **状态发生变更** - 订单状态变化时自动回调
2. **状态不变更，但骑手信息发生变更** - 骑手信息更新时回调
3. **状态不变更，但费用信息发生变更** - 费用信息变化时回调

## 功能特点

- ✅ 自动状态回调：订单状态变更时自动触发回调
- ✅ 完整日志：详细记录回调过程和结果
- ✅ 手动回调：提供API接口支持手动触发回调
- ✅ 签名验证：支持请求签名确保安全性
- ✅ 与现有实现一致：与麦芽田等其他平台保持相同的处理方式

## 配置说明

### 1. 环境变量配置

在 `.env` 文件中添加以下配置：

```bash
# 海博回调地址
HAIBO_CALLBACK_URL=https://pre-carrieropen.hiboos.com/api/delivery/statusCallback

# 海博调用协议配置
HAIBO_DEVELOPER_ID=your_developer_id
HAIBO_SECRET=your_secret

# 海博环境配置 (test, staging, production)
HAIBO_ENVIRONMENT=test

# 海博应用配置（兼容旧版本，可选）
HAIBO_APP_KEY=your_haibo_app_key
HAIBO_APP_SECRET=your_haibo_app_secret
```

### 2. 配置文件

配置文件位于 `config/haibo.php`，包含：
- 回调地址配置
- 海博调用协议配置
- 环境配置（测试/联调/生产）
- 状态映射配置
- 商品分类映射
- 订单来源映射

## 海博调用协议

本实现完全符合海博技术服务平台的调用协议规范：

### 请求规则

- **传输协议**：HTTPS
- **请求方式**：POST
- **参数格式**：application/x-www-form-urlencoded
- **字符编码**：UTF-8

### 公共参数

| 参数名称 | 参数类型 | 是否必填 | 参数描述 |
|----------|----------|----------|----------|
| developerId | String | 是 | 海博分配的配送商标识 |
| timestamp | long | 是 | 时间戳（GMT+8） |
| version | String | 是 | API协议版本（1.0） |
| sign | String | 是 | 签名计算结果 |

### 签名协议

1. 将所有参数（除sign、byte[]及空值参数）按参数名字典顺序排序
2. 将参数以"参数1值1参数2值2..."的顺序拼接
3. 按照"secret + 排序后的参数"的顺序连接
4. 对字符串进行SHA1加密并转为小写

### 环境地址

- **测试环境**：https://pre-carrieropen.hiboos.com
- **联调环境**：https://carrieropen-tool.hiboos.com
- **生产环境**：https://carrieropen.hiboos.com

## 使用方式

### 1. 自动回调

当订单状态发生变化时，系统会自动触发回调：

```php
// 订单状态变更时，会自动触发 DispatchOpenCallback 任务
// 该任务会检测到海博订单并直接调用海博回调服务
dispatch(new DispatchOpenCallback($order));
```

### 2. 手动回调

通过API接口手动触发回调：

```bash
POST /api/delivery/statusCallback
Content-Type: application/json

{
    "orderId": "HB_ORDER_123456"
}
```

### 3. 程序调用

直接调用服务方法：

```php
$haiboService = new HaiboService();
$success = $haiboService->deliveryStatusCallback(
    $userId,
    $orderNo,
    $outOrderNo,
    $status,
    $otherData
);
```

## 回调数据格式

向海博系统发送的回调数据格式（完全符合海博接口规范）：

```json
{
    "orderId": "HB_ORDER_123456",
    "carrierDeliveryId": "RM202501290001",
    "status": 20,
    "operateTime": **********,
    "riderName": "张骑手",
    "riderPhone": "13800138000",
    "riderPhoneType": 0,
    "riderLng": 120123456,
    "riderLat": 30123456,
    "actualFee": 15.50,
    "tipFee": 2.00,
    "tollFee": 0.00,
    "handlingFee": 0.00,
    "waitingFee": 0.00,
    "parkingFee": 0.00,
    "cancelReasonCode": 1,
    "cancelReasonDesc": "商户取消",
    "cancelFee": 0.00,
    "licensePlate": "京A12345",
    "orderImages": {
        "pickup": ["http://example.com/pickup1.jpg"],
        "arrived": ["http://example.com/arrived1.jpg"]
    },
    "otherCharges": {
        "夜间费": 2.00,
        "节假日费": 5.00
    }
}
```

### 字段说明

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| orderId | string | 是 | 海博平台订单号 |
| carrierDeliveryId | string | 是 | 配送商物流单号 |
| status | int | 是 | 运单状态码 |
| operateTime | long | 是 | 状态变更时间戳 |
| riderName | string | 条件必填 | 骑手姓名（已接单、已到店、已取货、已送达时必传） |
| riderPhone | string | 条件必填 | 骑手手机号（已接单、已到店、已取货、已送达时必传） |
| riderPhoneType | int | 条件必填 | 电话类型：0真实号，1隐私号 |
| riderLng | int | 条件必填 | 骑手经度（火星坐标*10^6） |
| riderLat | int | 条件必填 | 骑手纬度（火星坐标*10^6） |
| actualFee | double | 否 | 实际支付金额（元） |
| tipFee | double | 否 | 小费金额（元） |
| tollFee | double | 否 | 过路费（元） |
| handlingFee | double | 否 | 搬运费（元） |
| waitingFee | double | 否 | 等候费（元） |
| parkingFee | double | 否 | 停车费（元） |
| cancelReasonCode | int | 条件必填 | 取消原因code（取消状态时必填） |
| cancelReasonDesc | string | 条件必填 | 取消原因描述（取消状态时必填） |
| cancelFee | double | 否 | 取消扣费/违约金（元） |
| licensePlate | string | 否 | 车牌号（汽车配送必传） |
| orderImages | object | 否 | 订单图片（pickup取件照片，arrived送达照片） |
| otherCharges | object | 否 | 其他费用Map |

## 状态映射

系统内部状态到海博状态的映射：

| 系统状态 | 海博状态码 | 状态说明 |
|---------|-----------|----------|
| STATUS_WAITING_PAY | 10 | 已创建(待接单) |
| STATUS_PAID | 10 | 已创建(待接单) |
| STATUS_PICKUP | 20 | 骑手已接单 |
| STATUS_ARRIVE_PICKUP_POINT | 25 | 已到店 |
| STATUS_DELIVERY | 30 | 已取货 |
| STATUS_FINISH | 50 | 已送达 |
| STATUS_CANCEL | 99 | 已取消 |

## 错误处理

- **失败记录**：回调失败时记录详细错误日志
- **与现有实现一致**：采用与麦芽田相同的处理方式，不进行自动重试
- **手动重试**：可通过API接口手动触发重试

## 日志记录

所有回调相关的日志都记录在 `haibo` 频道中：

```bash
# 查看海博回调日志
tail -f storage/logs/haibo.log
```

## 注意事项

1. **已送达和已取消状态不支持再变更**：符合海博平台规则
2. **海博主动取消也需要回调**：系统会正确处理海博主动发起的取消
3. **费用信息完整回传**：每次回调都会传递最新的费用信息
4. **与现有实现保持一致**：采用与麦芽田相同的处理方式，不进行自动重试

## 故障排查

### 1. 回调失败

检查日志文件 `storage/logs/haibo.log`：

```bash
grep "海博配送状态回调失败" storage/logs/haibo.log
```

### 2. 配置问题

确认环境变量配置正确：

```bash
php artisan config:cache
php artisan config:clear
```

### 3. 队列问题

确认队列正常运行：

```bash
php artisan queue:work
```

## API接口文档

### 配送状态回调接口

**接口地址**：`POST /api/delivery/statusCallback`

**请求参数**：
- `orderId`（必填）：海博平台订单号

**响应格式**：
```json
{
    "code": 0,
    "message": "回调成功",
    "data": {
        "orderId": "HB_ORDER_123456",
        "carrierDeliveryId": "RM202501290001",
        "status": 20
    }
}
```

**错误码**：
- `0`：成功
- `1`：系统异常
- `2`：参数错误
- `101`：订单不存在
