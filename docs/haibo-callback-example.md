# 海博配送状态回调示例

## 配置示例

### 1. 环境变量配置 (.env)

```bash
# 海博回调地址 - 配送商系统向海博系统回调的地址
HAIBO_CALLBACK_URL=https://api.haibo.com/api/delivery/statusCallback

# 海博应用配置
HAIBO_APP_KEY=your_haibo_app_key
HAIBO_APP_SECRET=your_haibo_app_secret
```

## 回调示例

### 1. 骑手已接单状态回调

```json
{
    "orderId": "HB5352399293513728",
    "carrierDeliveryId": "DOHB5352399293513728",
    "status": 20,
    "operateTime": 1747052942,
    "riderName": "张骑手",
    "riderPhone": "13800138000",
    "riderPhoneType": 0,
    "riderLng": 120123456,
    "riderLat": 30123456,
    "actualFee": 15.50,
    "tipFee": 2.00,
    "tollFee": 0.00,
    "handlingFee": 0.00,
    "waitingFee": 0.00,
    "parkingFee": 0.00
}
```

### 2. 已送达状态回调（包含照片）

```json
{
    "orderId": "HB5352399293513728",
    "carrierDeliveryId": "DOHB5352399293513728",
    "status": 50,
    "operateTime": 1747055942,
    "riderName": "张骑手",
    "riderPhone": "13800138000",
    "riderPhoneType": 0,
    "riderLng": 120125456,
    "riderLat": 30125456,
    "actualFee": 15.50,
    "tipFee": 2.00,
    "tollFee": 0.00,
    "handlingFee": 0.00,
    "waitingFee": 0.00,
    "parkingFee": 0.00,
    "orderImages": {
        "pickup": [
            "https://example.com/images/pickup1.jpg",
            "https://example.com/images/pickup2.jpg"
        ],
        "arrived": [
            "https://example.com/images/arrived1.jpg",
            "https://example.com/images/arrived2.jpg"
        ]
    }
}
```

### 3. 已取消状态回调

```json
{
    "orderId": "HB5352399293513728",
    "carrierDeliveryId": "DOHB5352399293513728",
    "status": 99,
    "operateTime": 1747052942,
    "actualFee": 15.50,
    "tollFee": 0.00,
    "handlingFee": 0.00,
    "waitingFee": 0.00,
    "parkingFee": 0.00,
    "cancelReasonCode": 1,
    "cancelReasonDesc": "商户取消",
    "cancelFee": 2.00
}
```

### 4. 汽车配送回调（包含车牌号）

```json
{
    "orderId": "HB5352399293513728",
    "carrierDeliveryId": "DOHB5352399293513728",
    "status": 30,
    "operateTime": **********,
    "riderName": "李司机",
    "riderPhone": "13900139000",
    "riderPhoneType": 0,
    "riderLng": 120124456,
    "riderLat": 30124456,
    "actualFee": 25.50,
    "tipFee": 5.00,
    "tollFee": 2.00,
    "handlingFee": 3.00,
    "waitingFee": 0.00,
    "parkingFee": 1.00,
    "licensePlate": "京A12345",
    "otherCharges": {
        "夜间费": 2.00,
        "节假日费": 3.00
    }
}
```

## 海博响应示例

### 成功响应

```json
{
    "code": 0,
    "message": "成功",
    "data": null
}
```

### 失败响应

```json
{
    "code": 2,
    "message": "参数验证失败: 骑手信息不能为空",
    "data": null
}
```

## 状态码说明

| 状态码 | 状态名称 | 说明 |
|--------|----------|------|
| 10 | 已创建(待接单) | 订单已创建，等待骑手接单 |
| 20 | 骑手已接单 | 骑手已接单，准备前往取货 |
| 25 | 已到店 | 骑手已到达取货地点 |
| 30 | 已取货 | 骑手已取货，开始配送 |
| 50 | 已送达 | 订单已完成配送 |
| 99 | 已取消 | 订单已取消 |

## 取消原因码说明

| 原因码 | 说明 |
|--------|------|
| 1 | 商户取消 |
| 2 | 用户在渠道或更上一层的取消 |
| 3 | 因为各系统原因发起的取消 |
| 4 | 配送商的骑手发起的取消 |
| 5 | 配送商侧发起的取消配送 |
| 99 | 其他原因 |

## 注意事项

1. **坐标格式**：经纬度需要乘以10^6转为整数（火星坐标系）
2. **金额格式**：所有金额字段保留两位小数
3. **图片限制**：单个场景最多5张图片，URL总长度不超过1000字符
4. **必填字段**：特定状态下的骑手信息为必填
5. **状态顺序**：不可跳过状态，必须按顺序回传
