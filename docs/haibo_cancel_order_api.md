# 海博平台取消订单接口文档

## 接口概述

海博平台取消订单接口，用于取消已创建的配送订单。该接口参考了maiyatian的取消配送实现，确保与现有系统的兼容性。

## 接口信息

- **接口路径**: `/api/haibo/cancel`
- **请求方式**: `POST`
- **Content-Type**: `application/json`

## 请求参数

| 字段 | 描述 | 是否必填 | 类型 | 示例值 | 规则 |
|------|------|----------|------|--------|------|
| orderId | 海博平台订单号 | 是 | string | HB_ORDER_20250128001 | 长度为18-45个字符。可作为唯一识别单号，建议配送商作为商家订单号使用。 |
| carrierShopId | 配送商门店id | 否 | string | HB_123 | 可选参数 |
| cancelReasonCode | 取消原因code | 是 | int | 1007 | 详细枚举见下方取消原因Code |
| cancelReasonDesc | 取消原因说明 | 是 | string | 订单取消 | 最长不超过256个字符 |
| carrierCode | 配送商code | 否 | string | haibo | 可选参数 |

### 取消原因Code枚举

| Code | 描述 |
|------|------|
| 1000 | 订单信息填写错误 |
| 1001 | 已用其他发单工具配送 |
| 1002 | 没有骑手接单 |
| 1005 | 配送方系统超时 |
| 1007 | 订单取消 |
| 1008 | 其他原因 |

## 返回参数

| 字段 | 描述 | 是否必填 | 类型 | 示例值 | 规则 |
|------|------|----------|------|--------|------|
| code | 结果code | 是 | int | 0 | 0：成功。非0：失败，详情查看message字段 |
| message | 失败的描述信息 | 是 | string | 成功 | - |
| data | 结果 | 否 | map | {"cancelFee":0.0} | 如果取消成功则需要传入 |

### 取消扣费字段说明

| 字段 | 描述 | 是否必填 | 类型 | 示例值 | 规则 |
|------|------|----------|------|--------|------|
| cancelFee | 取消扣费(违约金) | 否 | double | 0.0 | 如果未产生费用则传入0 |

## 接口返回结果Code

| Code | 描述 |
|------|------|
| 0 | 成功 |
| 1001 | 参数错误 |
| 1002 | 无运力 |
| 9999 | 系统错误 |

## 请求示例

```json
{
    "orderId": "HB_ORDER_20250128001",
    "cancelReasonCode": 1007,
    "cancelReasonDesc": "订单取消",
    "carrierShopId": "HB_123"
}
```

## 响应示例

### 成功响应

```json
{
    "code": 0,
    "message": "成功",
    "data": {
        "cancelFee": 0.0
    }
}
```

### 失败响应

```json
{
    "code": 1001,
    "message": "订单不存在",
    "data": null
}
```

## 业务逻辑说明

1. **订单查找**: 根据 `orderId` 查找对应的海博平台订单
2. **状态检查**: 检查订单是否可以取消（由 `O2oErrandOrderService::refundOrder` 方法处理）
3. **退款处理**: 调用现有的退款逻辑进行订单取消和退款
4. **违约金计算**: 根据实际支付金额和退款金额计算取消扣费

### 违约金计算规则

取消扣费 = 实际支付金额 - 退款金额

- 实际支付金额 = 配送费 + 小费 + 保价费
- 退款金额由 `O2oErrandOrderService::refundOrder` 方法根据订单状态计算
- 如果计算结果为负数，则取消扣费为0

### 订单状态与退款金额关系

根据 `O2oErrandOrderService::refundOrder` 的逻辑：

- **待接单状态**: 全额退款（无违约金）
- **已接单状态**: 全额退款（无违约金）
- **已到达取货点**: 全额退款（无违约金）
- **配送中状态**: 全额退款（无违约金）
- **其他状态**: 不允许取消

## 错误处理

- 订单不存在时返回参数错误
- 订单已退款时由 `O2oErrandOrderService` 抛出异常，转换为系统错误
- 参数验证失败时返回参数错误
- 其他异常情况返回系统错误

## 注意事项

1. 该接口完全复用了现有的 `O2oErrandOrderService::refundOrder` 方法，确保与maiyatian等其他平台的取消逻辑保持一致
2. 取消原因的映射逻辑参考了maiyatian的实现
3. 违约金计算基于实际的退款金额，而不是简单的百分比计算
4. 接口支持幂等性，重复调用已取消的订单会返回相应的错误信息
