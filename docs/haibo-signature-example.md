# 海博签名算法示例

## 签名协议说明

海博技术服务平台使用SHA1签名算法来验证API请求的合法性。

### 签名步骤

1. **参数过滤**：排除sign参数、byte[]参数及值为空的参数
2. **字典排序**：将参数按参数名的字典顺序排序
3. **参数拼接**：将参数以"参数1值1参数2值2..."的顺序拼接
4. **添加密钥**：按照"secret + 排序后的参数"的顺序连接
5. **SHA1加密**：对字符串进行SHA1加密并转为小写

## 官方示例

### 输入参数

```
secret: test

系统参数：
developerId=test
timestamp=1477395862
version=1.0

应用参数：
number=123
string=测试
double=123.123
boolean=true
empty=          (空值，会被过滤)
```

### 签名计算过程

#### 1. 参数过滤和排序

过滤空值参数后，按字典顺序排序：
- boolean=true
- developerId=test
- double=123.123
- number=123
- string=测试
- timestamp=1477395862
- version=1.0

#### 2. 参数拼接

```
booleantruedeveloperIdtestdouble123.123number123string测试timestamp1477395862version1.0
```

#### 3. 添加密钥

```
testbooleantruedeveloperIdtestdouble123.123number123string测试timestamp1477395862version1.0
```

#### 4. SHA1加密

```php
$signString = 'testbooleantruedeveloperIdtestdouble123.123number123string测试timestamp1477395862version1.0';
$sign = strtolower(sha1($signString));
// 结果: 8943ba698f4b009f80dc2fd69ff9b313381263bd
```

### 期望结果

```
sign = 8943ba698f4b009f80dc2fd69ff9b313381263bd
```

## 代码实现

### PHP实现

```php
public function generateHaiboSignature(array $params): string
{
    // 1. 过滤参数
    $filteredParams = [];
    foreach ($params as $key => $value) {
        // 排除 sign 参数和空值参数
        if ($key === 'sign' || $value === null || $value === '') {
            continue;
        }
        
        // 数组和对象转为JSON字符串
        if (is_array($value) || is_object($value)) {
            $value = json_encode($value, JSON_UNESCAPED_UNICODE);
        }
        
        $filteredParams[$key] = $value;
    }
    
    // 2. 按参数名字典顺序排序
    ksort($filteredParams);
    
    // 3. 参数拼接
    $signString = '';
    foreach ($filteredParams as $key => $value) {
        $signString .= $key . $value;
    }
    
    // 4. 添加密钥
    $signString = $this->secret . $signString;
    
    // 5. SHA1加密并转小写
    return strtolower(sha1($signString));
}
```

## 测试验证

可以使用以下方法验证签名算法是否正确：

```php
$haiboService = new HaiboService();
$testResult = $haiboService->testSignature();

if ($testResult['is_correct']) {
    echo "签名算法正确";
} else {
    echo "签名算法错误";
    echo "期望: " . $testResult['expected_sign'];
    echo "实际: " . $testResult['generated_sign'];
}
```

## 注意事项

1. **字符编码**：统一使用UTF-8编码
2. **参数值类型**：布尔值转为字符串（true/false）
3. **数组参数**：转为JSON字符串
4. **空值处理**：空字符串和null值会被过滤
5. **大小写**：最终签名转为小写
6. **密钥保护**：secret密钥需要妥善保管，避免泄露

## 常见问题

### Q: 签名验证失败怎么办？

A: 检查以下几点：
1. 参数是否正确过滤（排除空值和sign参数）
2. 参数排序是否按字典顺序
3. 参数拼接格式是否正确（key+value）
4. secret是否正确
5. SHA1加密结果是否转为小写

### Q: 中文参数如何处理？

A: 中文参数使用UTF-8编码，直接参与签名计算，无需额外处理。

### Q: 数组参数如何处理？

A: 数组和对象参数需要转为JSON字符串后参与签名计算。
