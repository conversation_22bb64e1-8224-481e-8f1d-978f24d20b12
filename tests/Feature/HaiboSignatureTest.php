<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;

class HaiboSignatureTest extends TestCase
{
    /**
     * 测试海博签名算法是否正确
     * 
     * 根据海博文档示例：
     * secret: test
     * 系统参数：developerId=test timestamp=1477395862 version=1.0
     * 应用参数：number=123 string=测试 double=123.123 boolean=true empty=
     * 期望签名：8943ba698f4b009f80dc2fd69ff9b313381263bd
     */
    public function test_haibo_signature_generation()
    {
        // 临时设置测试配置
        Config::set('haibo.developer_id', 'test');
        Config::set('haibo.secret', 'test');

        // 构建测试参数
        $testParams = [
            'developerId' => 'test',
            'timestamp' => 1477395862,
            'version' => '1.0',
            'number' => 123,
            'string' => '测试',
            'double' => 123.123,
            'boolean' => true,
            'empty' => '', // 空值会被过滤掉
        ];

        // 生成签名
        $generatedSign = $this->generateHaiboSignature($testParams);
        $expectedSign = '8943ba698f4b009f80dc2fd69ff9b313381263bd';

        $this->assertEquals($expectedSign, $generatedSign, '海博签名算法验证失败');
    }

    /**
     * 测试海博API验签中间件
     */
    public function test_haibo_api_signature_validation()
    {
        // 设置测试配置
        Config::set('haibo.developer_id', 'test_dev_id');
        Config::set('haibo.secret', 'test_secret');

        $timestamp = time();
        $params = [
            'developerId' => 'test_dev_id',
            'timestamp' => $timestamp,
            'version' => '1.0',
            'orderId' => 'TEST_ORDER_123',
            'recipientLng' => 116.397128,
            'recipientLat' => 39.916527,
            'senderLng' => 116.407395,
            'senderLat' => 39.904211,
            'carrierShopId' => 'SHOP_001',
        ];

        // 生成正确的签名
        $sign = $this->generateHaiboSignature($params);
        $params['sign'] = $sign;

        // 测试询价接口
        $response = $this->postJson('/api/haibo/valuating', $params);

        // 验证不是签名错误（应该是其他业务逻辑错误）
        $this->assertNotEquals(400, $response->getStatusCode(), '签名验证失败');
    }

    /**
     * 测试错误的签名
     */
    public function test_haibo_api_invalid_signature()
    {
        // 设置测试配置
        Config::set('haibo.developer_id', 'test_dev_id');
        Config::set('haibo.secret', 'test_secret');

        $params = [
            'developerId' => 'test_dev_id',
            'timestamp' => time(),
            'version' => '1.0',
            'orderId' => 'TEST_ORDER_123',
            'sign' => 'invalid_signature',
        ];

        // 测试询价接口
        $response = $this->postJson('/api/haibo/valuating', $params);

        // 验证返回签名错误
        $response->assertStatus(400);
        $response->assertJson([
            'code' => 1,
            'message' => '签名验证失败',
            'data' => null
        ]);
    }

    /**
     * 测试缺少必需参数
     */
    public function test_haibo_api_missing_required_params()
    {
        $params = [
            'orderId' => 'TEST_ORDER_123',
            // 缺少 developerId, timestamp, version, sign
        ];

        // 测试询价接口
        $response = $this->postJson('/api/haibo/valuating', $params);

        // 验证返回参数错误
        $response->assertStatus(400);
        $response->assertJsonStructure([
            'code',
            'message',
            'data'
        ]);
    }

    /**
     * 生成海博签名 - 复制中间件中的逻辑用于测试
     *
     * @param array $params
     * @return string
     */
    private function generateHaiboSignature(array $params): string
    {
        $secret = config('haibo.secret');

        // 1. 将所有系统参数及业务参数（其中sign，byte[]及值为空的参数除外）按照参数名的字典顺序排序
        $filteredParams = [];
        foreach ($params as $key => $value) {
            // 排除 sign 参数和空值参数
            if ($key === 'sign' || $value === null || $value === '') {
                continue;
            }

            // 数组和对象转为JSON字符串
            if (is_array($value) || is_object($value)) {
                $value = json_encode($value, JSON_UNESCAPED_UNICODE);
            }

            // 布尔值转换
            if (is_bool($value)) {
                $value = $value ? 'true' : 'false';
            }

            $filteredParams[$key] = $value;
        }

        // 按参数名字典顺序排序
        ksort($filteredParams);

        // 2. 将参数以参数1值1参数2值2...的顺序拼接
        $signString = '';
        foreach ($filteredParams as $key => $value) {
            $signString .= $key . $value;
        }

        // 3. 按照secret + 排序后的参数的顺序进行连接，得到加密前的字符串
        $signString = $secret . $signString;

        // 4. 对加密前的字符串进行sha1加密并转为小写字符串，得到签名
        return strtolower(sha1($signString));
    }
}
